import React, { useState } from 'react';
import styles from '../index.module.less';

// 导入表格标题背景图片
import tableTitleImg from '../../../assets/IncomingInspectionDashboard/table_title.png';

interface MaterialFilterContentProps {
  dataSet: any;
  onSelect: (record: any) => void;
  selectedRecord: any;
  materialPageData: any[]; // 从主组件传入的物料数据
  onQuery: (materialCode: string, materialName: string) => void; // 新增查询回调
}

const MaterialFilterContent = ({
  dataSet,
  onSelect,
  selectedRecord,
  materialPageData,
  onQuery,
}: MaterialFilterContentProps) => {
  const [materialCodeFilter, setMaterialCodeFilter] = useState('');
  const [materialNameFilter, setMaterialNameFilter] = useState('');

  // 使用从主组件传入的真实数据
  const allRecords = materialPageData.map(item => ({
    code: item.materialCode,
    description: item.material,
    materialId: item.materialId,
    ...item, // 保留原始数据
  }));

  // 显示所有数据，不进行前端筛选
  const filteredRecords = allRecords;

  return (
    <div className={styles.materialFilterList}>
      {/* 筛选条件区域 */}
      <div className={styles.materialFilterControls}>
        <div className={styles.filterRow}>
          <div className={styles.filterItem}>
            <label className={styles.filterLabel}>物料编码:</label>
            <input
              type="text"
              className={styles.filterInput}
              placeholder="请输入物料编码"
              value={materialCodeFilter}
              onChange={e => setMaterialCodeFilter(e.target.value)}
            />
          </div>
          <div className={styles.filterItem}>
            <label className={styles.filterLabel}>物料名称:</label>
            <input
              type="text"
              className={styles.filterInput}
              placeholder="请输入物料名称"
              value={materialNameFilter}
              onChange={e => setMaterialNameFilter(e.target.value)}
            />
          </div>
          <div className={styles.filterButtonBox}>
            <button
              type="button"
              className={styles.filterButton}
              onClick={() => {
                setMaterialCodeFilter('');
                setMaterialNameFilter('');
              }}
            >
              重置
            </button>
            <button
              type="button"
              className={styles.filterButton}
              onClick={() => {
                onQuery(materialCodeFilter, materialNameFilter);
              }}
            >
              查询
            </button>
          </div>
        </div>
      </div>

      {/* 列表头部 */}
      <div
        className={styles.materialFilterListHeader}
        style={{
          backgroundImage: `url(${tableTitleImg})`,
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
        }}
      >
        <span>物料编码</span>
        <span>物料描述</span>
      </div>

      {/* 列表内容 */}
      <div className={styles.materialFilterListBody}>
        {filteredRecords.length > 0 ? (
          filteredRecords.map((record: any) => {
            const recordCode = record.code;
            const recordDescription = record.description;

            return (
              <div
                key={recordCode}
                className={`${styles.materialFilterListRow} ${
                  selectedRecord?.code === recordCode ? styles.selected : ''
                }`}
                onClick={() => onSelect(record)}
              >
                <span>{recordCode}</span>
                <span>{recordDescription}</span>
              </div>
            );
          })
        ) : (
          <div className={styles.noDataRow}>
            <span>暂无匹配的物料数据</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default MaterialFilterContent;
